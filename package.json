{"name": "NodeSeeker", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "$npm_execpath run build && vite preview", "deploy": "$npm_execpath run build && wrangler deploy", "cf-typegen": "wrangler types --env-interface CloudflareBindings"}, "dependencies": {"hono": "^4.8.2"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.2.3", "vite": "^6.3.5", "vite-ssr-components": "^0.3.0", "wrangler": "^4.17.0"}}